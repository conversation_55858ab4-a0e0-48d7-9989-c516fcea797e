/* eslint-disable react/prop-types */
import {Link, useParams} from "react-router-dom";
import GameItem from "./componets/GameItem";
import Pagination from "./componets/utility/Pagination";
import MenuSideBar from "./componets/MenuSideBar";
import MainHeader from "./componets/MainHeader";
import RightSideBar from "./componets/RightSideBar";
import CategoryFilter from "./componets/CategoryFilter";
import {useEffect, useState} from "react";
import MainFooter from "./componets/MainFooter";
import {backgroundImageFun} from "../vbrae-utils/lib/getIcons.jsx";
import {useSellerOffers} from "../hooks/offers/useSellerOffers.js";
import {generateQuery} from "../vbrae-utils/lib/misc.js";
import {getUserId} from "../vbrae-utils/index.js";
import Spinner from "../components/common/Spinner.jsx";
import EmptyGameItem from "./componets/EmptyGameItem.jsx";
import {useCategoryDetails} from "../hooks/categories/useCategoryDetails.js";

export default function Category() {

  const { activeCategory, id } = useParams();
  const {category} = useCategoryDetails({_id: id})

  const userId = getUserId();

  const [page, setPage] = useState(1);
  const [selectedRegion, setSelectedRegion] = useState("");
  const [selectedLanguage, setSelectedLanguage] = useState("");
  const [selectedGenre, setSelectedGenre] = useState("");
  const [prices, setPrices] = useState([]);
  const [selectedBoxes, setSelectedBoxes] = useState({});
  const [filledCards, setFilledCards] = useState([]);

  const { offers, offersLoading } = useSellerOffers({
    query: generateQuery({
      staticString: `category=${activeCategory}${userId ? `&userId=${userId}` : ''}`,
      limit: 12,
      page,
      selectedRegion,
      selectedLanguage,
      selectedGenre,
      prices,
      selectedBoxes
    }),
  });

  useEffect(() => {

    if(!offers) return;

    fillGrid();
    window.addEventListener("resize", fillGrid); // Refill on resize
    return () => window.removeEventListener("resize", fillGrid);
  }, [offers]);

  const fillGrid = () => {
    const container = document.querySelector(".game_list_con");
    const containerWidth = container.clientWidth; // Get container width
    const itemsPerRow = Math.floor(containerWidth / 250); // Calculate items that can fit

    const totalItems = Math.ceil(offers.data.length / itemsPerRow) * itemsPerRow; // Calculate total items needed
    const placeholders = totalItems - offers.data.length; // Calculate number of placeholders needed
    const filled = [...offers.data, ...Array(placeholders).fill(null)]; // Create filled array

    setFilledCards(filled);
  };


  const resetFilters = () => {
    setSelectedRegion("");
    setSelectedLanguage("");
    setSelectedGenre("");
    setPrices([]);
    setSelectedBoxes({})
  }

  const [isFilterOpen, setIsFilterOpen] = useState(false);

  function OpenFilter() {
    setIsFilterOpen(true);
  }
  function CloseFilter() {
    setIsFilterOpen(false);
  }

  useEffect(() => {
    resetFilters();
  }, [id]);

  return (
    <>
      <div className="d-flex main_house_con">
        <MenuSideBar makeShort={true} activeLink={activeCategory} />

        <div
          className="col housing_con d-flex flex-column"
          style={{
            backgroundImage: backgroundImageFun(activeCategory),
          }}>
          <MainHeader activeLink={activeCategory} />

          <div className="housing d-flex gap-1 position-relative">
            <div id="scrollable-section" className="col main_section">
              <div className="d-flex align-items-center gap-2 mb-3">
                <Link to={"/"}>
                  <span className="crumb_icon">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="1em"
                      height="1em"
                      viewBox="0 0 24 24">
                      <path
                        fill="currentColor"
                        d="M13.45 2.533a2.25 2.25 0 0 0-2.9 0L3.8 8.228a2.25 2.25 0 0 0-.8 1.72v9.305c0 .966.784 1.75 1.75 1.75h3a1.75 1.75 0 0 0 1.75-1.75V15.25c0-.68.542-1.232 1.217-1.25h2.566a1.25 1.25 0 0 1 1.217 1.25v4.003c0 .966.784 1.75 1.75 1.75h3a1.75 1.75 0 0 0 1.75-1.75V9.947a2.25 2.25 0 0 0-.8-1.72z"
                      />
                    </svg>
                  </span>
                </Link>
                <Link>
                  <p className="crumb_link">/ {activeCategory.toUpperCase()}</p>
                </Link>
              </div>

              <div className="main_cont d-flex gap-4 position-relative">
                <CategoryFilter
                    isOpen={isFilterOpen}
                    onClose={CloseFilter}
                    selectedRegion={selectedRegion}
                    setSelectedRegion={setSelectedRegion}
                    selectedLanguage={selectedLanguage}
                    prices={prices}
                    selectedGenre={selectedGenre}
                    setSelectedLanguage={setSelectedLanguage}
                    setSelectedGenre={setSelectedGenre}
                    setPrices={setPrices}
                    setSelectedBoxes={setSelectedBoxes}
                    selectedBoxes={selectedBoxes}
                />
                <div className="w-100">
                  <p className="cate_filter_head mb-2">34 ITEMS</p>

                  <h3 className="cate_header mb-2">{activeCategory.toUpperCase()}</h3>
                  <p className="cate_desc d-none d-xl-block mb-4">
                    {category?.description}
                  </p>
                  <hr className="cate_hr d-none d-xl-block mb-4"/>

                  <div
                      className="col d-xl-flex flex-row-reverse align-items-start align-items-xl-center gap-3 gap-xl-4 mb-4">
                    <div className="col col-xl-3 d-flex gap-2 align-items-center mb-3 mb-xl-0">
                      <p
                          onClick={OpenFilter}
                          className="col d-flex d-lg-none cate_filter_tigger align-items-center gap-2">
                        <svg
                            xmlns="http://www.w3.org/2000/svg"
                            width="1em"
                            height="1em"
                            viewBox="0 0 24 24">
                          <g fill="none" fillRule="evenodd">
                            <path
                                d="m12.593 23.258l-.011.002l-.071.035l-.02.004l-.014-.004l-.071-.035q-.016-.005-.024.005l-.004.01l-.017.428l.005.02l.01.013l.104.074l.015.004l.012-.004l.104-.074l.012-.016l.004-.017l-.017-.427q-.004-.016-.017-.018m.265-.113l-.013.002l-.185.093l-.01.01l-.003.011l.018.43l.005.012l.008.007l.201.093q.019.005.029-.008l.004-.014l-.034-.614q-.005-.018-.02-.022m-.715.002a.02.02 0 0 0-.027.006l-.006.014l-.034.614q.001.018.017.024l.015-.002l.201-.093l.01-.008l.004-.011l.017-.43l-.003-.012l-.01-.01z"/>
                            <path
                                fill="currentColor"
                                d="M3 4.5A1.5 1.5 0 0 1 4.5 3h15A1.5 1.5 0 0 1 21 4.5v2.086A2 2 0 0 1 20.414 8L15 13.414v7.424a1.1 1.1 0 0 1-1.592.984l-3.717-1.858A1.25 1.25 0 0 1 9 18.846v-5.432L3.586 8A2 2 0 0 1 3 6.586z"
                            />
                          </g>
                        </svg>
                        Filter by
                        <span className="d-flex justify-content-center align-items-center ms-auto">
                          {Object.values(selectedBoxes).flat().length}
                        </span>
                      </p>
                      <select className="col col-lg-6 col-xl cate_filter_sort_sel">
                        <option value="">Most recent</option>
                      </select>
                    </div>
                    <div className="col d-flex gap-2 overflow-auto hide_scroll">
                      {Object.keys(selectedBoxes).length > 0 &&
                          <span className="cate_filter_tag" onClick={resetFilters}>
                              Clear all
                            <svg
                                className="ms-1"
                                xmlns="http://www.w3.org/2000/svg"
                                width="1em"
                                height="1em"
                                viewBox="0 0 32 32">
                              <path
                                  fill="currentColor"
                                  d="M16 2C8.2 2 2 8.2 2 16s6.2 14 14 14s14-6.2 14-14S23.8 2 16 2m5.4 21L16 17.6L10.6 23L9 21.4l5.4-5.4L9 10.6L10.6 9l5.4 5.4L21.4 9l1.6 1.6l-5.4 5.4l5.4 5.4z"
                              />
                            </svg>
                          </span>}

                      {Object.entries(selectedBoxes).map(([key, value], index) => {
                        if (key === "delivery") {
                          return (
                              <span className="cate_filter_tag text-capitalize"
                                    onClick={() => setSelectedBoxes(prev => {
                                      const newState = { ...prev };
                                      delete newState.delivery;
                                      return newState;
                                    })}
                                    key={index}>
                                    {key} {value.join('/')}
                                                            <svg className="ms-1" xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" viewBox="0 0 32 32">
                                      <path fill="currentColor" d="M16 2C8.2 2 2 8.2 2 16s6.2 14 14 14s14-6.2 14-14S23.8 2 16 2m5.4 21L16 17.6L10.6 23L9 21.4l5.4-5.4L9 10.6L10.6 9l5.4 5.4L21.4 9l1.6 1.6l-5.4 5.4l5.4 5.4z"/>
                                    </svg>
                                  </span>
                          );
                        }
                        else if(key === 'stock[gt]') {
                          return (
                              <span className="cate_filter_tag text-capitalize"
                                    onClick={() => setSelectedBoxes(prev => {
                                      const newState = { ...prev };
                                      delete newState[`stock[gt]`];
                                      return newState;
                                    })}
                                    key={index}>
                                    In stock
                                <svg className="ms-1" xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" viewBox="0 0 32 32">
                                      <path fill="currentColor" d="M16 2C8.2 2 2 8.2 2 16s6.2 14 14 14s14-6.2 14-14S23.8 2 16 2m5.4 21L16 17.6L10.6 23L9 21.4l5.4-5.4L9 10.6L10.6 9l5.4 5.4L21.4 9l1.6 1.6l-5.4 5.4l5.4 5.4z"/>
                                    </svg>
                                  </span>
                          );
                        }
                         else if(key === 'isHot') {
                          return (
                              <span className="cate_filter_tag text-capitalize"
                                    onClick={() => setSelectedBoxes(prev => {
                                      const newState = { ...prev };
                                      delete newState[`isHot`];
                                      return newState;
                                    })}
                                    key={index}>
                                   Is Hot
                                <svg className="ms-1" xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" viewBox="0 0 32 32">
                                      <path fill="currentColor" d="M16 2C8.2 2 2 8.2 2 16s6.2 14 14 14s14-6.2 14-14S23.8 2 16 2m5.4 21L16 17.6L10.6 23L9 21.4l5.4-5.4L9 10.6L10.6 9l5.4 5.4L21.4 9l1.6 1.6l-5.4 5.4l5.4 5.4z"/>
                                    </svg>
                                  </span>
                          );
                        }
                       
                        else if(key === 'instantDelivery' && value[0]) {
                          return (
                              <span className="cate_filter_tag text-capitalize" onClick={() => setSelectedBoxes(prev => {
                                const newState = { ...prev };
                                delete newState.instantDelivery;
                                return newState;
                              })}
                                    key={index}>
                                    Pre Order
                                <svg className="ms-1" xmlns="http://www.w3.org/2000/svg" width="1em" height="1em"
                                     viewBox="0 0 32 32">
                                      <path fill="currentColor"
                                            d="M16 2C8.2 2 2 8.2 2 16s6.2 14 14 14s14-6.2 14-14S23.8 2 16 2m5.4 21L16 17.6L10.6 23L9 21.4l5.4-5.4L9 10.6L10.6 9l5.4 5.4L21.4 9l1.6 1.6l-5.4 5.4l5.4 5.4z"/>
                                    </svg>
                                  </span>
                          )
                        }

else if (key === 'listingType' && Array.isArray(value)) {
  return value.map((type, i) => (
    <span
      className="cate_filter_tag text-capitalize"
      onClick={() =>
        setSelectedBoxes((prev) => {
          const updated = prev.listingType.filter((item) => item !== type);
          const newState = { ...prev, listingType: updated };
          if (updated.length === 0) {
            delete newState.listingType;
          }
          return newState;
        })
      }
      key={`${type}-${i}`}
    >
      {type === 'key' ? 'Codes/Keys' : type === 'account' ? 'Account' : type}
      <svg
        className="ms-1"
        xmlns="http://www.w3.org/2000/svg"
        width="1em"
        height="1em"
        viewBox="0 0 32 32"
      >
        <path
          fill="currentColor"
          d="M16 2C8.2 2 2 8.2 2 16s6.2 14 14 14s14-6.2 14-14S23.8 2 16 2m5.4 21L16 17.6L10.6 23L9 21.4l5.4-5.4L9 10.6L10.6 9l5.4 5.4L21.4 9l1.6 1.6l-5.4 5.4l5.4 5.4z"
        />
      </svg>
    </span>
  ));
}


                        else if(key === 'region'){
                          return (
                              <span className="cate_filter_tag text-capitalize" onClick={() => setSelectedBoxes(prev => {
                                const newState = { ...prev };
                                delete newState.region;
                                return newState;
                              })}
                                    key={index}>
                                    {selectedBoxes?.region[0]}
                                <svg className="ms-1" xmlns="http://www.w3.org/2000/svg" width="1em" height="1em"
                                     viewBox="0 0 32 32">
                                      <path fill="currentColor"
                                            d="M16 2C8.2 2 2 8.2 2 16s6.2 14 14 14s14-6.2 14-14S23.8 2 16 2m5.4 21L16 17.6L10.6 23L9 21.4l5.4-5.4L9 10.6L10.6 9l5.4 5.4L21.4 9l1.6 1.6l-5.4 5.4l5.4 5.4z"/>
                                    </svg>
                                  </span>
                          )
                        }
                        else if(key === 'subcategory'){
                          return (
                              <span className="cate_filter_tag text-capitalize" onClick={() => setSelectedBoxes(prev => {
                                const newState = { ...prev };
                                delete newState.subcategory;
                                return newState;
                              })}
                                    key={index}>
                                    {selectedBoxes?.subcategory[0]}
                                <svg className="ms-1" xmlns="http://www.w3.org/2000/svg" width="1em" height="1em"
                                     viewBox="0 0 32 32">
                                      <path fill="currentColor"
                                            d="M16 2C8.2 2 2 8.2 2 16s6.2 14 14 14s14-6.2 14-14S23.8 2 16 2m5.4 21L16 17.6L10.6 23L9 21.4l5.4-5.4L9 10.6L10.6 9l5.4 5.4L21.4 9l1.6 1.6l-5.4 5.4l5.4 5.4z"/>
                                    </svg>
                                  </span>
                          )
                        }
                        return null;
                      })}

                      {prices[0] && <span className="cate_filter_tag" onClick={() => setPrices([undefined, prices[1]])}>
                      Min: ${prices[0]}
                        <svg
                            className="ms-1"
                            xmlns="http://www.w3.org/2000/svg"
                            width="1em"
                            height="1em"
                            viewBox="0 0 32 32">
                        <path
                            fill="currentColor"
                            d="M16 2C8.2 2 2 8.2 2 16s6.2 14 14 14s14-6.2 14-14S23.8 2 16 2m5.4 21L16 17.6L10.6 23L9 21.4l5.4-5.4L9 10.6L10.6 9l5.4 5.4L21.4 9l1.6 1.6l-5.4 5.4l5.4 5.4z"
                        />
                      </svg>
                    </span>}

                      {prices[1] && <span className="cate_filter_tag" onClick={() => setPrices([prices[0], undefined])}>
                      Max: ${prices[1]}
                        <svg
                            className="ms-1"
                            xmlns="http://www.w3.org/2000/svg"
                            width="1em"
                            height="1em"
                            viewBox="0 0 32 32">
                        <path
                            fill="currentColor"
                            d="M16 2C8.2 2 2 8.2 2 16s6.2 14 14 14s14-6.2 14-14S23.8 2 16 2m5.4 21L16 17.6L10.6 23L9 21.4l5.4-5.4L9 10.6L10.6 9l5.4 5.4L21.4 9l1.6 1.6l-5.4 5.4l5.4 5.4z"
                        />
                      </svg>
                    </span>}

                      {selectedRegion && <span className="cate_filter_tag" onClick={() => setSelectedRegion("")}>
                      {selectedRegion}
                        <svg
                            className="ms-1"
                            xmlns="http://www.w3.org/2000/svg"
                            width="1em"
                            height="1em"
                            viewBox="0 0 32 32">
                        <path
                            fill="currentColor"
                            d="M16 2C8.2 2 2 8.2 2 16s6.2 14 14 14s14-6.2 14-14S23.8 2 16 2m5.4 21L16 17.6L10.6 23L9 21.4l5.4-5.4L9 10.6L10.6 9l5.4 5.4L21.4 9l1.6 1.6l-5.4 5.4l5.4 5.4z"
                        />
                      </svg>
                    </span>}

                      {selectedLanguage && <span className="cate_filter_tag" onClick={() => setSelectedLanguage("")}>
                      {selectedLanguage}
                        <svg
                            className="ms-1"
                            xmlns="http://www.w3.org/2000/svg"
                            width="1em"
                            height="1em"
                            viewBox="0 0 32 32">
                        <path
                            fill="currentColor"
                            d="M16 2C8.2 2 2 8.2 2 16s6.2 14 14 14s14-6.2 14-14S23.8 2 16 2m5.4 21L16 17.6L10.6 23L9 21.4l5.4-5.4L9 10.6L10.6 9l5.4 5.4L21.4 9l1.6 1.6l-5.4 5.4l5.4 5.4z"
                        />
                      </svg>
                    </span>}

                      {selectedGenre && <span className="cate_filter_tag" onClick={() => setSelectedGenre("")}>
                      {selectedGenre}
                        <svg
                            className="ms-1"
                            xmlns="http://www.w3.org/2000/svg"
                            width="1em"
                            height="1em"
                            viewBox="0 0 32 32">
                        <path
                            fill="currentColor"
                            d="M16 2C8.2 2 2 8.2 2 16s6.2 14 14 14s14-6.2 14-14S23.8 2 16 2m5.4 21L16 17.6L10.6 23L9 21.4l5.4-5.4L9 10.6L10.6 9l5.4 5.4L21.4 9l1.6 1.6l-5.4 5.4l5.4 5.4z"
                        />
                      </svg>
                    </span>}

                    </div>
                  </div>

                  {(offers && !offersLoading) ? <>
                    <div className="col game_list_con row justify-content-start g-3 mx mb-4">
                      {filledCards.map((card, index) => (
                          <>
                            {card !== null ? (
                                <GameItem key={index} {...card} />
                            ) : (
                                <EmptyGameItem />
                            )}
                          </>
                      ))}
                    </div>

                    {!!offers.pagination.pages && <div className="col d-flex justify-content-center">
                      <Pagination
                          totalPages={offers.pagination.pages}
                          currentPage={page}
                          pageClick={(page) => setPage(page)}
                          nextPage={() => setPage(page + 1)}
                          prevPage={() => setPage(page - 1)}/>
                    </div>}
                  </> : <Spinner />}

                </div>
              </div>

              <div className="col d-lg-none">
                <MainFooter/>
              </div>
            </div>
            <RightSideBar/>
          </div>
        </div>
      </div>
    </>
  );
}
