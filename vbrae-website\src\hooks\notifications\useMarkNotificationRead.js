import {useQueryClient} from "react-query";
import {patchRequest} from "../../vbrae-utils/index.js";
import {useState} from "react";

export const useMarkNotificationRead = () => {
    const queryClient = useQueryClient();
    const [markingAsRead, setMarkingAsRead] = useState(false);

    const markNotificationAsRead = async (notificationId) => {
        if (!notificationId) return;

        setMarkingAsRead(true);

        // Optimistically update the cache
        queryClient.setQueryData(['notifications'], (old) => {
            if (!old?.data) return old;

            return {
                ...old,
                data: old.data.map(notification =>
                    notification._id === notificationId
                        ? { ...notification, isRead: true }
                        : notification
                )
            };
        });

        // Make the API call - using the existing notifications endpoint with the ID
        try {
            await patchRequest({
                url: `notifications/${notificationId}`,
                data: { isRead: true },
                useAuth: true,
            });

            // Invalidate notifications to refresh the list
            queryClient.invalidateQueries(['notifications']);
        } catch (error) {
            console.error('Failed to mark notification as read:', error);
            // Revert optimistic update on error
            queryClient.invalidateQueries(['notifications']);
        } finally {
            setMarkingAsRead(false);
        }
    };

    return { markNotificationAsRead, markingAsRead };
};
