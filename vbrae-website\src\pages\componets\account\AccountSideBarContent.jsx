/* eslint-disable react/prop-types */
import {Link} from "react-router-dom";
import {useProfile} from "../../../hooks/auth/useProfile.js";
import {useConversations} from "../../../hooks/conversation/useConversations.js";

export default function AccountSideBarContent({ activeLink }) {

  const {user} = useProfile();
  const hasUser = user?.role === "seller";

  const {conversations} = useConversations();

  const nameToShow = user?.role === "client" ? "unreadClient" : "unreadSeller";

  const totalUnreadMessages = (conversations ?? []).reduce((acc, cur) => {
    acc += cur[nameToShow];
    return acc;
  }, 0)

  return (
    <>
      <div className="col">
        <div className="mb-4">
          {hasUser && <>
            <Link to={"/account"} className="d-block mb-3">
              <p
                  className={
                      " menu_sidebar_link d-flex gap-3 align-items-center " +
                      (activeLink == "dashboard" && "active")
                  }>
                <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="20"
                    height="20"
                    viewBox="0 0 20 20"
                    fill="currentColor">
                  <path d="M12.4 12.4C11.5163 12.4 10.8 13.1163 10.8 14V16.4C10.8 17.2837 11.5163 18 12.4 18H12.4192C13.4217 18 14.2819 17.9474 15.0006 17.7876C15.7286 17.6258 16.3595 17.3438 16.8517 16.8517C17.3438 16.3595 17.6258 15.7286 17.7876 15.0006C17.861 14.6706 17.9094 14.3355 17.9411 13.9983C18.0241 13.1186 17.2837 12.4 16.4 12.4H12.4Z" />
                  <path d="M2 12.4192V10.8C2 9.91632 2.71634 9.2 3.6 9.2H7.6C8.48368 9.2 9.2 9.91632 9.2 10.8V16.4C9.2 17.2837 8.48368 18 7.6 18H7.5808C6.5783 18 5.7181 17.9474 4.99932 17.7876C4.27146 17.6258 3.64052 17.3438 3.14834 16.8517C2.65617 16.3595 2.37418 15.7286 2.21242 15.0006C2.05267 14.2819 2 13.4217 2 12.4192Z" />
                  <path d="M18 9.2C18 10.0837 17.2837 10.8 16.4 10.8H12.4C11.5163 10.8 10.8 10.0837 10.8 9.2V3.6C10.8 2.71634 11.5163 2 12.4 2H12.4192C13.4217 2 14.2819 2.05267 15.0006 2.21242C15.7286 2.37418 16.3595 2.65617 16.8517 3.14834C17.3438 3.64052 17.6258 4.27146 17.7876 4.99932C17.9474 5.7181 18 6.5783 18 7.5808V9.2Z" />
                  <path d="M9.2 3.6C9.2 2.71634 8.48368 2 7.6 2H7.5808C6.5783 2 5.7181 2.05267 4.99932 2.21242C4.27146 2.37418 3.64052 2.65617 3.14834 3.14834C2.65617 3.64052 2.37418 4.27146 2.21242 4.99932C2.13905 5.32944 2.09063 5.6645 2.05885 6.00168C1.97593 6.88144 2.71634 7.6 3.6 7.6H7.6C8.48368 7.6 9.2 6.88366 9.2 6V3.6Z" />
                </svg>
                Dashboard
              </p>
            </Link>

            <hr className="menu_sidebar_line my-4" />
          </>}

          {hasUser && <>
            <Link to={"/account/new-offers"} className="d-block mb-3">
              <p
                  className={
                      "menu_sidebar_link d-flex gap-3 align-items-center " +
                      (activeLink == "new-offers" && "active")
                  }>
                <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="20"
                    height="20"
                    viewBox="0 0 24 24">
                  <path
                      fill="currentColor"
                      d="M12 2C6.486 2 2 6.486 2 12s4.486 10 10 10s10-4.486 10-10S17.514 2 12 2m5 11h-4v4h-2v-4H7v-2h4V7h2v4h4z"
                  />
                </svg>
                New Offers
              </p>
            </Link>
            <Link to={"/account/offers"} className="d-block mb-3">
              <p
                  className={
                      "menu_sidebar_link d-flex gap-3 align-items-center " +
                      (activeLink == "my-offers" && "active")
                  }>
                <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="20"
                    height="20"
                    viewBox="0 0 20 20"
                    fill="currentColor">
                  <path
                      fillRule="evenodd"
                      clipRule="evenodd"
                      d="M3.63773 4.05656C2.53774 4.26452 1.63919 5.05415 1.29857 6.11218C1.21922 6.35867 0.265444 11.5117 0.0420187 12.901C-0.13712 14.0149 0.266272 15.2421 1.06615 16.0168C2.4411 17.3485 4.57038 17.3245 5.89138 15.9624C6.04521 15.8038 6.46785 15.2833 6.83052 14.8057L7.48992 13.9375L12.4596 13.9366L13.1187 14.8045C14.0608 16.0453 14.4352 16.4036 15.1038 16.7049C16.5894 17.3742 18.4211 16.8445 19.3304 15.4826C19.6243 15.0424 19.9218 14.2319 19.9219 13.8712C19.9219 13.7991 19.9395 13.7291 19.9609 13.7155C19.9824 13.702 20 13.5561 20 13.3915C20 13.2268 19.9849 13.1016 19.9663 13.1134C19.9478 13.1251 19.9226 13.0473 19.9102 12.9406C19.8494 12.4137 18.738 6.37023 18.6539 6.1088C18.4061 5.3386 17.8285 4.67202 17.1023 4.31814C16.5301 4.03932 16.4287 4.02421 15.0052 4.0058C14.1609 3.99487 13.6809 4.00394 13.5762 4.03279C13.4608 4.06463 13.2499 4.22619 12.7903 4.63504L12.1621 5.19378L7.7907 5.19464L7.1632 4.63657C6.74549 4.2651 6.48811 4.06499 6.39348 4.03814C6.18164 3.97794 3.97552 3.9927 3.63773 4.05656ZM14.5428 7.37269C13.7143 7.60076 13.0894 8.28865 12.9138 9.16584C12.6782 10.3431 13.4678 11.5464 14.6431 11.801C15.3951 11.9639 16.089 11.7494 16.6474 11.1812C17.113 10.7074 17.309 10.2321 17.3091 9.57682C17.3092 9.39246 17.2824 9.13625 17.2496 9.00739C17.0509 8.226 16.4117 7.57425 15.6461 7.37223C15.3511 7.2944 14.8262 7.29462 14.5428 7.37269ZM4.78783 6.58226C4.71273 6.62431 4.61207 6.72872 4.56411 6.81429C4.48665 6.95259 4.47689 7.02593 4.47622 7.4758L4.4755 8.98181L2.93913 8.99489C2.31324 9.01018 2.20409 9.05787 2.08899 9.36617C2.00672 9.58655 2.03579 9.77749 2.17802 9.95048C2.33877 10.146 2.47803 10.1857 3.00288 10.1856L4.46923 10.1855L4.48192 11.7217C4.49295 12.1887 4.50495 12.272 4.57499 12.3679C4.71948 12.5657 4.84929 12.638 5.06013 12.6379C5.28014 12.6379 5.41465 12.5582 5.55754 12.3434C5.63726 12.2235 5.64534 12.1646 5.64543 11.7014L5.64552 10.1914L7.16945 10.1783C7.73827 10.164 7.80677 10.1399 7.98895 9.89018C8.10612 9.72959 8.09982 9.4101 7.97593 9.23235C7.83378 9.02834 7.69312 8.98741 7.1346 8.98752L5.64575 8.98761L5.64584 7.48464C5.64595 6.92118 5.60307 6.76957 5.40216 6.62318C5.23827 6.50376 4.96088 6.48529 4.78783 6.58226ZM14.7184 8.58507C14.3168 8.75007 14.0726 9.12857 14.0718 9.58695C14.0708 10.1896 14.5086 10.6327 15.105 10.6325C15.9855 10.6324 16.4621 9.56617 15.8884 8.88014C15.6122 8.54993 15.1113 8.42359 14.7184 8.58507ZM0.******** 13.44C0.******** 13.6156 0.******** 13.6825 0.0172767 13.5886C0.0255639 13.4947 0.0254737 13.351 0.0171021 13.2693C0.******** 13.1875 0.******** 13.2643 0.******** 13.44Z"
                  />
                </svg>
                My Offers
              </p>
            </Link>
            <Link to={"/account/sales"} className="d-block mb-3">
              <p
                  className={
                      "menu_sidebar_link d-flex gap-3 align-items-center " +
                      (activeLink == "sales" && "active")
                  }>
                <svg
                    width="20"
                    height="20"
                    viewBox="0 0 20 20"
                    fill="currentColor"
                    xmlns="http://www.w3.org/2000/svg">
                  <path
                      fillRule="evenodd"
                      clipRule="evenodd"
                      d="M9.97955 1.15039C9.46242 1.37907 9.22828 1.66459 8.94857 2.40744L8.73081 2.98581L8.86526 3.13631C8.9392 3.21911 10.7082 5.23388 12.7963 7.61363L16.593 11.9404L17.0949 11.8672C17.7736 11.7683 18.0804 11.6606 18.385 11.4147C18.9658 10.9455 19.1605 10.1721 18.86 9.52713C18.7505 9.29195 18.6549 9.18054 14.0414 3.91175C11.867 1.4284 11.8451 1.4051 11.5091 1.21988C11.0247 0.952898 10.482 0.928209 9.97955 1.15039ZM7.50211 5.41256C6.07007 7.84485 4.14168 10.0757 1.82731 11.9776C1.43785 12.2977 1.31007 12.4314 1.20783 12.626C0.955631 13.1059 0.932328 13.6033 1.14139 14.042C1.32642 14.4303 3.5765 16.9871 3.91856 17.1977C4.25954 17.4077 4.71412 17.4866 5.12766 17.4077C5.52406 17.332 5.72265 17.2253 6.26506 16.7968C8.60417 14.9489 11.4602 13.4418 14.4043 12.502L14.9756 12.3196L11.5661 8.43483C9.69079 6.29822 8.13383 4.53252 8.10617 4.51111C8.07267 4.4851 7.87071 4.78655 7.50211 5.41256ZM11.595 14.6185C10.9134 14.9555 10.2813 15.2859 9.86288 15.5239C9.1378 15.9365 8.34952 16.4444 8.36226 16.491C8.37861 16.5507 10.0421 18.4395 10.2009 18.5787C10.412 18.7636 10.7376 18.916 11.0453 18.9741C11.2604 19.0146 11.4049 19.0088 11.6952 18.9482C12.114 18.8607 12.3538 18.7255 12.832 18.3072C13.4239 17.7896 13.6122 17.0031 13.2949 16.3736C13.1691 16.1239 11.7942 14.5339 11.7262 14.5593C11.7003 14.569 11.6413 14.5957 11.595 14.6185Z"
                  />
                </svg>
                Sales
              </p>
            </Link>
          </>}
          <Link to={"/account/orders"} className="d-block mb-3">
            <p
              className={
                "menu_sidebar_link d-flex gap-3 align-items-center " +
                (activeLink == "orders" && "active")
              }>
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="20"
                height="20"
                viewBox="0 0 20 20"
                fill="currentColor">
                <path d="M14.3636 3.76923C14.3636 3.38688 14.6736 3.07692 15.0559 3.07692H15.6818C16.4098 3.07692 17 3.66709 17 4.3951V17C17 18.1046 16.1046 19 15 19H5C3.89543 19 3 18.1046 3 17V4.39511C3 3.66709 3.59017 3.07692 4.31818 3.07692H4.94406C5.32641 3.07692 5.63636 3.38688 5.63636 3.76923C5.63636 4.15158 5.94632 4.46154 6.32867 4.46154H13.6713C14.0537 4.46154 14.3636 4.15158 14.3636 3.76923ZM4.90909 9.30769C4.90909 9.69004 5.21905 10 5.6014 10H14.3986C14.781 10 15.0909 9.69004 15.0909 9.30769C15.0909 8.92534 14.781 8.61539 14.3986 8.61539H5.6014C5.21905 8.61539 4.90909 8.92534 4.90909 9.30769ZM4.90909 14.8462C4.90909 15.2285 5.21905 15.5385 5.6014 15.5385H14.3986C14.781 15.5385 15.0909 15.2285 15.0909 14.8462C15.0909 14.4638 14.781 14.1538 14.3986 14.1538H5.6014C5.21905 14.1538 4.90909 14.4638 4.90909 14.8462ZM8.12937 3.07692C7.55585 3.07692 7.09091 2.61199 7.09091 2.03846C7.09091 1.46494 7.55584 1 8.12937 1H11.8706C12.4442 1 12.9091 1.46494 12.9091 2.03846C12.9091 2.61199 12.4442 3.07692 11.8706 3.07692H8.12937Z" />
              </svg>
              Orders
            </p>
          </Link>

          <hr className="menu_sidebar_line my-4" />

          {hasUser && <>
            <Link to={"/account/balance"} className="d-block mb-3">
              <p
                  className={
                      "menu_sidebar_link d-flex gap-3 align-items-center " +
                      (activeLink == "balance" && "active")
                  }>
                <svg
                    width="20"
                    height="20"
                    viewBox="0 0 20 20"
                    fill="currentColor"
                    xmlns="http://www.w3.org/2000/svg">
                  <path
                      d="M1 14.7727C1 15.3634 1.23705 15.93 1.65901 16.3476C2.08097 16.7653 2.65326 17 3.25 17H16.75C17.3467 17 17.919 16.7653 18.341 16.3476C18.7629 15.93 19 15.3634 19 14.7727V8.64773H1V14.7727ZM3.65179 11.75C3.65179 11.4335 3.77878 11.1301 4.00483 10.9063C4.23088 10.6825 4.53746 10.5568 4.85714 10.5568H6.78571C7.10539 10.5568 7.41198 10.6825 7.63803 10.9063C7.86408 11.1301 7.99107 11.4335 7.99107 11.75V12.5455C7.99107 12.8619 7.86408 13.1654 7.63803 13.3892C7.41198 13.6129 7.10539 13.7386 6.78571 13.7386H4.85714C4.53746 13.7386 4.23088 13.6129 4.00483 13.3892C3.77878 13.1654 3.65179 12.8619 3.65179 12.5455V11.75ZM16.75 3H3.25C2.65326 3 2.08097 3.23466 1.65901 3.65235C1.23705 4.07005 1 4.63656 1 5.22727V6.26136H19V5.22727C19 4.63656 18.7629 4.07005 18.341 3.65235C17.919 3.23466 17.3467 3 16.75 3Z"/>
                </svg>
                Balance
              </p>
            </Link>
            <hr className="menu_sidebar_line my-4"/>
          </>}

          <Link to={"/account/messages"} className="d-block mb-3">
            <p
              className={
                "menu_sidebar_link d-flex gap-3 align-items-center " +
                (activeLink == "messages" && "active")
              }>
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="20"
                height="20"
                viewBox="0 0 20 20">
                <g fill="currentColor">
                  <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0 0 16 4H4a2 2 0 0 0-1.997 1.884"></path>
                  <path d="m18 8.118l-8 4l-8-4V14a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2z"></path>
                </g>
              </svg>
              Messages
              {totalUnreadMessages > 0 && <span className="badge d-flex justify-content-center align-items-center ms-auto">
                {totalUnreadMessages}
              </span>}
            </p>
          </Link>
          <Link to={"/account/wishlist"} className="d-block mb-3">
            <p
              className={
                "menu_sidebar_link d-flex gap-3 align-items-center " +
                (activeLink == "wishlist" && "active")
              }>
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="20"
                height="20"
                viewBox="0 0 20 20"
                fill="currentColor">
                <path d="M10 14.0553L14.944 17L13.632 11.45L18 7.71579L12.248 7.23421L10 2L7.752 7.23421L2 7.71579L6.368 11.45L5.056 17L10 14.0553Z" />
              </svg>
              Wishlist
            </p>
          </Link>
          <Link to={"/account/notifications"} className="d-block mb-3">
            <p
              className={
                "menu_sidebar_link d-flex gap-3 align-items-center " +
                (activeLink == "notification" && "active")
              }>
              <svg
                width="20"
                height="20"
                viewBox="0 0 20 20"
                fill="currentColor"
                xmlns="http://www.w3.org/2000/svg">
                <path d="M12.4615 16.4285C12.4615 17.1105 12.2022 17.7646 11.7406 18.2468C11.2789 18.7291 10.6528 19 10 19C9.34716 19 8.72106 18.7291 8.25943 18.2468C7.7978 17.7646 7.53846 17.1105 7.53846 16.4285H12.4615ZM16.1538 15.1428H2.73846C2.54261 15.1428 2.35478 15.0615 2.21629 14.9169C2.0778 14.7722 2 14.576 2 14.3714C2 14.1668 2.0778 13.9706 2.21629 13.8259C2.35478 13.6812 2.54261 13.6 2.73846 13.6H3.84615V8.71419C3.84615 5.60274 5.96308 3.00557 8.77538 2.41414C8.75821 2.23536 8.77709 2.05481 8.83081 1.88413C8.88453 1.71344 8.9719 1.55642 9.08729 1.42317C9.20267 1.28993 9.34351 1.18343 9.50072 1.11055C9.65792 1.03766 9.82801 1 10 1C10.172 1 10.3421 1.03766 10.4993 1.11055C10.6565 1.18343 10.7973 1.28993 10.9127 1.42317C11.0281 1.55642 11.1155 1.71344 11.1692 1.88413C11.2229 2.05481 11.2418 2.23536 11.2246 2.41414C12.6155 2.70969 13.8659 3.49824 14.7642 4.64631C15.6625 5.79438 16.1534 7.23143 16.1538 8.71419V13.6H17.2615C17.4574 13.6 17.6452 13.6812 17.7837 13.8259C17.9222 13.9706 18 14.1668 18 14.3714C18 14.576 17.9222 14.7722 17.7837 14.9169C17.6452 15.0615 17.4574 15.1428 17.2615 15.1428H16.1538Z" />
              </svg>
              Notification
            </p>
          </Link>
          <Link to={"/account/profile"} className="d-block mb-3">
            <p
              className={
                "menu_sidebar_link d-flex gap-3 align-items-center " +
                (activeLink == "settings" && "active")
              }>
              <svg
                width="20"
                height="20"
                viewBox="0 0 20 20"
                fill="currentColor"
                xmlns="http://www.w3.org/2000/svg">
                <path d="M10.0033 7.5493C9.40347 7.5493 8.84179 7.78266 8.41652 8.20915C7.99325 8.63565 7.75855 9.19894 7.75855 9.80046C7.75855 10.402 7.99325 10.9653 8.41652 11.3918C8.84179 11.8162 9.40347 12.0516 10.0033 12.0516C10.6031 12.0516 11.1647 11.8162 11.59 11.3918C12.0133 10.9653 12.248 10.402 12.248 9.80046C12.248 9.19894 12.0133 8.63565 11.59 8.20915C11.3823 7.99924 11.1351 7.83281 10.8627 7.71953C10.5903 7.60626 10.2981 7.54839 10.0033 7.5493ZM18.274 12.287L16.9621 11.1624C17.0243 10.7802 17.0564 10.3899 17.0564 10.0016C17.0564 9.61337 17.0243 9.22107 16.9621 8.84085L18.274 7.71627C18.3731 7.63119 18.444 7.51786 18.4774 7.39137C18.5107 7.26487 18.5048 7.1312 18.4606 7.00813L18.4425 6.95583C18.0815 5.94329 17.5405 5.00473 16.8457 4.18563L16.8096 4.14338C16.7253 4.0439 16.6128 3.97239 16.4871 3.93828C16.3614 3.90416 16.2284 3.90904 16.1055 3.95226L14.4766 4.53366C13.8748 4.03877 13.2048 3.64849 12.4787 3.3769L12.1637 1.66891C12.14 1.54024 12.0777 1.42187 11.9853 1.32952C11.8928 1.23717 11.7746 1.17522 11.6462 1.15189L11.592 1.14183C10.5489 0.952724 9.44961 0.952724 8.40649 1.14183L8.35232 1.15189C8.22393 1.17522 8.10566 1.23717 8.01322 1.32952C7.92077 1.42187 7.85854 1.54024 7.83477 1.66891L7.51783 3.38495C6.79853 3.65869 6.12845 4.04802 5.53389 4.53769L3.89298 3.95226C3.77015 3.90869 3.637 3.90364 3.51123 3.93778C3.38546 3.97191 3.27303 4.04363 3.18888 4.14338L3.15277 4.18563C2.45927 5.00559 1.9184 5.94392 1.55599 6.95583L1.53794 7.00813C1.44767 7.2596 1.52189 7.54125 1.72449 7.71627L3.05247 8.85292C2.99028 9.23113 2.96019 9.61739 2.96019 9.99962C2.96019 10.3859 2.99028 10.7721 3.05247 11.1463L1.72851 12.283C1.6294 12.3681 1.55847 12.4814 1.52515 12.6079C1.49182 12.7344 1.49768 12.868 1.54195 12.9911L1.56 13.0434C1.92309 14.0553 2.45869 14.9908 3.15678 15.8136L3.19289 15.8559C3.27725 15.9553 3.38968 16.0269 3.51538 16.061C3.64107 16.0951 3.77412 16.0902 3.89699 16.047L5.5379 15.4616C6.13569 15.9544 6.80168 16.3447 7.52184 16.6143L7.83879 18.3303C7.86255 18.459 7.92479 18.5774 8.01723 18.6697C8.10967 18.7621 8.22794 18.824 8.35634 18.8474L8.4105 18.8574C9.46388 19.0475 10.5426 19.0475 11.596 18.8574L11.6502 18.8474C11.7786 18.824 11.8969 18.7621 11.9893 18.6697C12.0817 18.5774 12.144 18.459 12.1677 18.3303L12.4827 16.6223C13.2089 16.3487 13.8789 15.9605 14.4807 15.4656L16.1095 16.047C16.2324 16.0906 16.3655 16.0956 16.4913 16.0615C16.6171 16.0273 16.7295 15.9556 16.8136 15.8559L16.8497 15.8136C17.5478 14.9868 18.0834 14.0553 18.4465 13.0434L18.4646 12.9911C18.5508 12.7417 18.4766 12.462 18.274 12.287ZM10.0033 13.3371C8.05544 13.3371 6.47671 11.7539 6.47671 9.80046C6.47671 7.84704 8.05544 6.26378 10.0033 6.26378C11.9511 6.26378 13.5298 7.84704 13.5298 9.80046C13.5298 11.7539 11.9511 13.3371 10.0033 13.3371Z" />
              </svg>
              Settings
            </p>
          </Link>
          <Link to={"/account/reviews"} className="d-block mb-3">
            <p
              className={
                "menu_sidebar_link d-flex gap-3 align-items-center " +
                (activeLink == "reviews" && "active")
              }>
              <svg
                width="20"
                height="20"
                viewBox="0 0 20 20"
                fill="currentColor"
                xmlns="http://www.w3.org/2000/svg">
                <path d="M16.3 7.22695C17.0161 7.22695 17.7028 7.5023 18.2092 7.99242C18.7155 8.48254 19 9.14729 19 9.84043V12.4539C19 13.147 18.7155 13.8118 18.2092 14.3019C17.7028 14.792 17.0161 15.0674 16.3 15.0674V16.9089C16.3 17.8323 15.1876 18.3368 14.455 17.7452L12.3742 15.0674H10C9.28392 15.0674 8.59716 14.792 8.09081 14.3019C7.58446 13.8118 7.3 13.147 7.3 12.4539V9.84043C7.3 9.14729 7.58446 8.48254 8.09081 7.99242C8.59716 7.5023 9.28392 7.22695 10 7.22695H16.3ZM13.6 2C14.3161 2 15.0028 2.27535 15.5092 2.76547C16.0155 3.25559 16.3 3.92034 16.3 4.61348V5.48464H9.1C8.14522 5.48464 7.22955 5.85177 6.55442 6.50526C5.87928 7.15876 5.5 8.04509 5.5 8.96927V12.4539C5.5 13.3634 5.86 14.1927 6.4504 14.813L5.5 16.503C4.7584 17.0413 3.7 16.5291 3.7 15.6318V13.3251C2.98392 13.3251 2.29716 13.0497 1.79081 12.5596C1.28446 12.0695 1 11.4047 1 10.7116V4.61348C1 3.92034 1.28446 3.25559 1.79081 2.76547C2.29716 2.27535 2.98392 2 3.7 2H13.6Z" />
              </svg>
              Reviews
            </p>
          </Link>
          <Link to={"/account/support"} className="d-block mb-3">
            <p
              className={
                "menu_sidebar_link d-flex gap-3 align-items-center " +
                (activeLink == "support" && "active")
              }>
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="20"
                height="20"
                viewBox="0 0 512 512">
                <path
                  fill="currentColor"
                  fillRule="evenodd"
                  d="M422.401 217.174c-6.613-67.84-46.72-174.507-170.666-174.507c-123.947 0-164.054 106.667-170.667 174.507c-23.2 8.805-38.503 31.079-38.4 55.893v29.867c0 32.99 26.744 59.733 59.733 59.733c32.99 0 59.734-26.744 59.734-59.733v-29.867c-.108-24.279-14.848-46.095-37.334-55.253c4.267-39.254 25.174-132.48 126.934-132.48s122.453 93.226 126.72 132.48c-22.44 9.178-37.106 31.009-37.12 55.253v29.867a59.95 59.95 0 0 0 33.92 53.76c-8.96 16.853-31.787 39.68-87.894 46.506c-11.215-17.03-32.914-23.744-51.788-16.023c-18.873 7.72-29.646 27.717-25.71 47.725s21.48 34.432 41.872 34.432a42.67 42.67 0 0 0 37.973-23.68c91.52-10.454 120.747-57.6 129.92-85.334c24.817-8.039 41.508-31.301 41.173-57.386v-29.867c.103-24.814-15.2-47.088-38.4-55.893m-302.933 85.76c0 9.425-7.641 17.066-17.067 17.066s-17.066-7.64-17.066-17.066v-29.867a17.067 17.067 0 1 1 34.133 0zm264.533-29.867c0-9.426 7.641-17.067 17.067-17.067s17.067 7.641 17.067 17.067v29.867c0 9.425-7.641 17.066-17.067 17.066s-17.067-7.64-17.067-17.066z"></path>
              </svg>
              Customer Support
            </p>
          </Link>
        </div>
      </div>

      <Link to={"/account/help-center"}>
        <p
          className={
            "menu_sidebar_help mt-auto " +
            (activeLink == "help-center" && "active")
          }>
          <img
            src={
              activeLink == "help-center"
                ? "../assets/images/icons/help_white.svg"
                : "../assets/images/icons/help.svg"
            }
            alt=""
            className="me-3"
          />
          Resolution Center
        </p>
      </Link>
    </>
  );
}
