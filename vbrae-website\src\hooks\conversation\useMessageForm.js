import * as Yup from 'yup';
import {useMutation} from "react-query";
import {useFormik} from "formik";
import {showError} from "../../vbrae-utils/index.js";
import {useLocation, useSearchParams} from "react-router-dom";
import {postMessage} from "../../api/conversation/postMessage.js";
import {uploadUrl} from "../../api/uploadUrl.js";

const schema = Yup.object().shape({
    content: Yup.string().required('Body is required'),
    attachments: Yup.array().of(Yup.mixed()).nullable(),
});

export default function useMessageForm() {

    const [searchParams] = useSearchParams();
    const conversationId = searchParams.get("conversationId");
    const location = useLocation().pathname;

    const initialValues = {
        content: '',
        attachments: []
    }

    const { mutateAsync } = useMutation(postMessage, {
        onError: (error)=> showError(error),
    });

    const uploadImage = async (image) => {
        if (image instanceof File) {
            const fileType = image.name.split('.').pop()?.toLowerCase() || 'unknown';
            const resignedResponse = await uploadUrl({ name: image.name, fileType });
            const { url: resignedUrl, path: filePath } = resignedResponse;

            await fetch(resignedUrl, {
                method: 'PUT',
                headers: { 'Content-Type': image.type, 'x-amz-acl': 'public-read' },
                body: image,
            });

            return filePath;
        }
        return image;
    };

    const formik = useFormik({
        initialValues,
        enableReinitialize: true,
        validationSchema: schema,
        onSubmit: async (values, { setSubmitting, resetForm }) => {
            setSubmitting(true);
            const imageUrls = await Promise.all(values.attachments.map(async (attachment) => await uploadImage(attachment)))
            const response = await mutateAsync({ ...values, conversationId, attachments: imageUrls, entityType: location.includes("support-details") ? "conversation-ticket":"conversation-offer" });
            setSubmitting(false);
            resetForm();
            if (response) {
                return
            }
        },
    });

    return { formik };
}
