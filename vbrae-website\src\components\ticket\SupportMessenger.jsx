import {formatDateTime} from "../../vbrae-utils/lib/time.js";
import {Link} from "react-router-dom";
import ReplyForm from "./ReplyForm.jsx";
import {useMessages} from "../../hooks/conversation/useMessages.js";
import {useProfile} from "../../hooks/auth/useProfile.js";
import Spinner from "../common/Spinner.jsx";

export default function SupportMessenger(){

    const {user} = useProfile();
    const {messages, messagesLoading} = useMessages();

    const loadingState = messagesLoading || !user;

    if(loadingState) return <Spinner />

    return (
        <>
            <div className="col d-flex flex-column">
                {messages.map((message, index) => {
                    const isMyMessage = message.sender._id === user._id;
                    return (
                        <div className={`col mb-3 ${isMyMessage ? 'ms-auto' : ''}`} key={index} id={`msg_${message._id}`}>
                            <div className={`d-flex align-items-center gap-2 mb-2 ${isMyMessage ? 'justify-content-end' : ''}`}>
                                <p className="acct_head_s">{message.sender.name}</p>
                                <p className="acct_chat_date">• {formatDateTime(message.createdAt)}</p>
                            </div>
                            <div className={`acct_chat_cont position-relative ${isMyMessage ? 'light' : 'dark'}`}>
                                <p className="acct_chat_text">
                                    {message.content}
                                </p>
                                {message.attachments.length > 0 && message.attachments.map((attachment, index) => (
                                    <Link to={attachment} title="File" target="_blank" key={index}>
                                        <span className="acct_chat_tag d-inline-flex align-items-center gap-1 mt-2 mx-1" key={index}>
                                          <svg
                                              xmlns="http://www.w3.org/2000/svg"
                                              width="1em"
                                              height="1em"
                                              viewBox="0 0 512 512">
                                                <path
                                                    fill="currentColor"
                                                    d="M416 64H96a64.07 64.07 0 0 0-64 64v256a64.07 64.07 0 0 0 64 64h320a64.07 64.07 0 0 0 64-64V128a64.07 64.07 0 0 0-64-64m-80 64a48 48 0 1 1-48 48a48.05 48.05 0 0 1 48-48M96 416a32 32 0 0 1-32-32v-67.63l94.84-84.3a48.06 48.06 0 0 1 65.8 1.9l64.95 64.81L172.37 416Zm352-32a32 32 0 0 1-32 32H217.63l121.42-121.42a47.72 47.72 0 0 1 61.64-.16L448 333.84Z"></path>
                                          </svg>
                                        Attachment {index + 1}
                                    </span>
                                    </Link>
                                ))}
                            </div>
                        </div>
                    )
                })}
            </div>

            <hr className="acct_fil_line2 my-3"/>

            <ReplyForm />
        </>
    )
}