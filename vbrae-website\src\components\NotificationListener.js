import {useEffect, useState} from 'react';
import {getPusherClient} from "../vbrae-utils/lib/pusher.js";
import {getUserId} from "../vbrae-utils/index.js";
import {useQueryClient} from "react-query";
import {useMessageNotification} from "../hooks/conversation/useMessageNotification.js";
import {useSearchParams} from "react-router-dom";

export const NotificationListener = () => {
    const [state, setState] = useState({})
    const queryClient = useQueryClient();
    const {refetch} = useMessageNotification(state);

    const [searchParams] = useSearchParams();
    const conversationId = searchParams.get("conversationId");

    useEffect(() => {
        if(Object.keys(state).length === 0) return;
        refetch().finally(()=> setState({}));
    }, [state]);

    useEffect(() => {
        const pusherClient = getPusherClient();
        const userId = getUserId();

        const channel = pusherClient.subscribe(`private-user-${userId}`);
        channel.bind('new-notification', (newNotification) => {
            if(conversationId === (newNotification.entityType === 'conversation-offer' ? newNotification.entityId : newNotification.entity._id)) return;
            newNotification.entityType.includes("conversation") && setState(newNotification)

            queryClient.setQueryData(['notifications'], (old) => {
                return {
                    ...(old ?? { data: [] }),
                    data: [newNotification, ...(old?.data ?? [])],
                };
            });
        });

        return () => {
            channel.unbind_all();
            channel.unsubscribe();
        };
    }, [conversationId, queryClient]);

    return null;
};